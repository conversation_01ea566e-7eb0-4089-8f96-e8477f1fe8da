@InProceedings{10.1007/978-3-031-82024-3_8,
author="<PERSON>, <PERSON>
and <PERSON>, <PERSON><PERSON><PERSON>
and <PERSON>, <PERSON>
and <PERSON>, <PERSON>
and <PERSON>, <PERSON>",
editor="<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>
and <PERSON>, <PERSON><PERSON>
and <PERSON>, <PERSON>
and <PERSON>, <PERSON><PERSON><PERSON><PERSON>
and <PERSON><PERSON><PERSON>, <PERSON>
and <PERSON>, <PERSON>",
title="An Improved YOLOv8-Based Rice Pest and Disease Detection",
booktitle="Advances in Computer Graphics",
year="2025",
publisher="Springer Nature Switzerland",
address="Cham",
pages="96--107",
abstract="While rice pests and diseases significantly impact crop yields, existing deep learning methods for their detection face challenges with accuracy and deployment complexity. Addressing these issues, this study proposes the YOLOv8-HSFPN, an advanced detection framework. Firstly, it features an innovative High-level Select Feature Pyramid Network (HSFPN) neck network that effectively integrates high-level and low-level feature sets for enhanced feature fusion. Secondly, the addition of a deformable self-attention module further refines the model's adaptability to the varying shapes and locations of targets, dynamically adjusting to the salient features. The proposed model has undergone comparative and ablation studies alongside YOLOv8, YOLOv9, and YOLOv5, confirming its improved accuracy and streamlined deployment. This integration results in a robust detection model that not only marks a significant leap in accuracy, evidenced by a 3{\%} empirical increase over the standard YOLOv8, but is also remarkably compact. At a mere 3.97MB, this substantial 49.87{\%} size reduction compared to its predecessors renders it exceptionally suitable for devices with limited computational resources, thereby enhancing its viability in practical, real-world applications.",
isbn="978-3-031-82024-3"
}

